<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格布局风格的文档列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            line-height: 1.5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-section {
            padding: 16px 20px;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .search-btn {
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .upload-btn {
            background: #52c41a !important;
        }
        
        .document-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
        }
        
        .document-title h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
        
        .document-subtitle {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 1.4;
        }
        
        .document-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            color: #595959;
        }
        
        .document-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .document-table th {
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: #8c8c8c;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .document-table th:first-child,
        .document-table td:first-child {
            padding-left: 20px;
        }
        
        .document-table th:last-child,
        .document-table td:last-child {
            padding-right: 20px;
        }
        
        .document-row {
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }
        
        .document-row:hover {
            background: #f8f9fa;
        }
        
        .document-row:last-child {
            border-bottom: none;
        }
        
        .document-row td {
            padding: 12px 16px;
            vertical-align: middle;
        }
        
        .row-index {
            color: #8c8c8c;
            font-size: 12px;
        }
        
        .name-cell {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .file-icon {
            color: #1890ff;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .file-name {
            color: #262626;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 300px;
        }
        
        .text-center {
            text-align: center;
            color: #595959;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-available { 
            background-color: #f6ffed; 
            color: #52c41a; 
            border: 1px solid #b7eb8f; 
        }
        
        .status-processing { 
            background-color: #fff7e6; 
            color: #fa8c16; 
            border: 1px solid #ffd591; 
        }
        
        .status-failed { 
            background-color: #fff2f0; 
            color: #ff4d4f; 
            border: 1px solid #ffccc7; 
        }
        
        .action-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 32px;
            height: 18px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 18px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #1890ff;
        }
        
        input:checked + .slider:before {
            transform: translateX(14px);
        }
        
        .more-btn {
            padding: 4px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .more-btn:hover {
            background: #f0f0f0;
        }
        
        .pagination {
            padding: 16px 20px;
            border-top: 1px solid #f0f0f0;
            background: #fafafa;
            display: flex;
            justify-content: center;
            font-size: 14px;
            color: #595959;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 搜索栏 -->
        <div class="search-section">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索" />
                <button class="search-btn">🔍 搜索</button>
                <button class="search-btn upload-btn">➕ 添加文档</button>
            </div>
        </div>
        
        <!-- 文档头部 -->
        <div class="document-header">
            <div class="document-title">
                <h3>文档</h3>
                <div class="document-subtitle">知识库中的所有文档，每个文档都可以进行编辑和删除操作</div>
            </div>
            <div class="document-actions">
                <button class="action-btn">📋 不显示</button>
            </div>
        </div>
        
        <!-- 文档表格 -->
        <table class="document-table">
            <thead>
                <tr>
                    <th width="40">
                        <input type="checkbox" class="checkbox" />
                    </th>
                    <th width="40">#</th>
                    <th>名称</th>
                    <th width="80">分段数量</th>
                    <th width="80">字符数</th>
                    <th width="80">可用分段</th>
                    <th width="140">上传时间 ▼</th>
                    <th width="80">状态</th>
                    <th width="80">操作</th>
                </tr>
            </thead>
            <tbody>
                <tr class="document-row">
                    <td><input type="checkbox" class="checkbox" /></td>
                    <td class="row-index">1</td>
                    <td>
                        <div class="name-cell">
                            <span class="file-icon">📄</span>
                            <span class="file-name">2024年秋季招聘中学数学岗位计划.pdf</span>
                        </div>
                    </td>
                    <td class="text-center">0</td>
                    <td class="text-center">2.6k</td>
                    <td class="text-center">0</td>
                    <td class="text-center">2025-05-18 23:04</td>
                    <td class="text-center">
                        <span class="status-tag status-available">可用</span>
                    </td>
                    <td class="text-center">
                        <div class="action-buttons">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <button class="more-btn">⋯</button>
                        </div>
                    </td>
                </tr>
                
                <tr class="document-row">
                    <td><input type="checkbox" class="checkbox" /></td>
                    <td class="row-index">2</td>
                    <td>
                        <div class="name-cell">
                            <span class="file-icon">📝</span>
                            <span class="file-name">08社会服务.docx</span>
                        </div>
                    </td>
                    <td class="text-center">0</td>
                    <td class="text-center">189</td>
                    <td class="text-center">0</td>
                    <td class="text-center">2025-05-16 05:14</td>
                    <td class="text-center">
                        <span class="status-tag status-available">可用</span>
                    </td>
                    <td class="text-center">
                        <div class="action-buttons">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <button class="more-btn">⋯</button>
                        </div>
                    </td>
                </tr>
                
                <tr class="document-row">
                    <td><input type="checkbox" class="checkbox" /></td>
                    <td class="row-index">3</td>
                    <td>
                        <div class="name-cell">
                            <span class="file-icon">📋</span>
                            <span class="file-name">07建设成效.docx</span>
                        </div>
                    </td>
                    <td class="text-center">2</td>
                    <td class="text-center">842</td>
                    <td class="text-center">2</td>
                    <td class="text-center">2025-05-16 05:14</td>
                    <td class="text-center">
                        <span class="status-tag status-processing">处理中</span>
                    </td>
                    <td class="text-center">
                        <div class="action-buttons">
                            <label class="switch">
                                <input type="checkbox">
                                <span class="slider"></span>
                            </label>
                            <button class="more-btn">⋯</button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <!-- 分页 -->
        <div class="pagination">
            共 3 项
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('表格布局风格的文档列表加载完成');
            
            // 搜索功能
            document.querySelector('.search-btn:first-of-type').addEventListener('click', function() {
                const searchValue = document.querySelector('.search-input').value;
                alert('🔍 搜索: ' + (searchValue || '全部文档'));
            });
            
            // 添加文档功能
            document.querySelector('.upload-btn').addEventListener('click', function() {
                alert('📁 打开文件选择对话框');
            });
            
            // 更多操作按钮
            document.querySelectorAll('.more-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const row = this.closest('.document-row');
                    const fileName = row.querySelector('.file-name').textContent;
                    alert(`操作菜单: ${fileName}\n- 编辑\n- 删除`);
                });
            });
            
            // 开关切换
            document.querySelectorAll('.switch input').forEach(input => {
                input.addEventListener('change', function() {
                    const row = this.closest('.document-row');
                    const fileName = row.querySelector('.file-name').textContent;
                    const enabled = this.checked;
                    alert(`${fileName} 已${enabled ? '启用' : '禁用'}`);
                });
            });
        });
    </script>
</body>
</html>
