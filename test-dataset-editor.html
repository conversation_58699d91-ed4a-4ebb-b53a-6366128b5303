<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DatasetEditor 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #1890ff;
        }
        .mock-form {
            display: grid;
            gap: 15px;
        }
        .form-item {
            display: flex;
            align-items: center;
        }
        .form-item label {
            width: 120px;
            font-weight: bold;
        }
        .form-item input, .form-item textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .mock-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .mock-table th, .mock-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        .mock-table th {
            background-color: #fafafa;
            font-weight: 600;
        }
        .mock-upload {
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            padding: 40px;
            text-align: center;
            background-color: #fafafa;
        }
        .status-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status-available { background-color: #f6ffed; color: #52c41a; }
        .status-processing { background-color: #fff7e6; color: #fa8c16; }
        .status-failed { background-color: #fff2f0; color: #ff4d4f; }
        .action-btn {
            margin-right: 8px;
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        .search-bar {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .search-btn {
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>DatasetEditor 功能测试页面</h1>
        
        <!-- 知识库基本信息 -->
        <div class="section">
            <h3>知识库基本信息</h3>
            <div class="mock-form">
                <div class="form-item">
                    <label>知识库名称:</label>
                    <input type="text" value="测试知识库" />
                </div>
                <div class="form-item">
                    <label>描述:</label>
                    <textarea rows="3">这是一个用于测试的知识库</textarea>
                </div>
                <div class="form-item">
                    <label>创建人员:</label>
                    <input type="text" value="管理员" disabled />
                </div>
                <div class="form-item">
                    <label>创建时间:</label>
                    <input type="text" value="2025-01-07 10:30:00" disabled />
                </div>
            </div>
        </div>

        <!-- 文档搜索 -->
        <div class="section">
            <h3>文档搜索</h3>
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="文档名称" />
                <button class="search-btn">搜索</button>
            </div>
        </div>

        <!-- 文档上传 -->
        <div class="section">
            <h3>文档上传</h3>
            <div class="mock-upload">
                <p>📁 拖拽文件至此，或者点击上传</p>
                <p style="color: #666; font-size: 14px;">支持 .pdf, .doc, .docx, .txt, .md 格式，最多上传10个文件</p>
            </div>
        </div>

        <!-- 文档列表 -->
        <div class="section">
            <h3>文档列表</h3>
            <table class="mock-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>名称</th>
                        <th>分类类型</th>
                        <th>字符数</th>
                        <th>分段数</th>
                        <th>上传时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>2024年秋季招聘中学数学岗位计划.pdf</td>
                        <td>通用</td>
                        <td>2.6k</td>
                        <td>0</td>
                        <td>2025-05-16 23:04</td>
                        <td><span class="status-tag status-available">可用</span></td>
                        <td>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>08社会服务.docx</td>
                        <td>通用</td>
                        <td>1.8k</td>
                        <td>0</td>
                        <td>2025-05-16 05:34</td>
                        <td><span class="status-tag status-available">可用</span></td>
                        <td>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>07建设成效.docx</td>
                        <td>通用</td>
                        <td>1.8k</td>
                        <td>2</td>
                        <td>2025-05-16 05:34</td>
                        <td><span class="status-tag status-processing">处理中</span></td>
                        <td>
                            <button class="action-btn">编辑</button>
                            <button class="action-btn">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 简单的交互测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DatasetEditor 测试页面加载完成');
            
            // 搜索按钮点击事件
            document.querySelector('.search-btn').addEventListener('click', function() {
                const searchValue = document.querySelector('.search-input').value;
                console.log('搜索文档:', searchValue);
                alert('搜索功能: ' + (searchValue || '全部文档'));
            });
            
            // 操作按钮点击事件
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent;
                    const row = this.closest('tr');
                    const fileName = row.cells[1].textContent;
                    console.log(`${action}文档:`, fileName);
                    alert(`${action}文档: ${fileName}`);
                });
            });
            
            // 上传区域点击事件
            document.querySelector('.mock-upload').addEventListener('click', function() {
                console.log('点击上传区域');
                alert('文件上传功能');
            });
        });
    </script>
</body>
</html>
