<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的文档管理UI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            line-height: 1.5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .header-icon {
            font-size: 20px;
            color: #1890ff;
        }
        
        .header-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
        
        .header-subtitle {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 2px;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .search-input {
            width: 240px;
            padding: 8px 12px 8px 32px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23999' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: 8px center;
            background-size: 16px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .batch-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .batch-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .selected-count {
            font-size: 12px;
            color: #1890ff;
            font-weight: 500;
        }
        
        .batch-right {
            display: flex;
            gap: 8px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .document-list {
            padding: 0;
        }
        
        .document-item {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .document-item:hover {
            background: #f8f9fa;
            transform: translateX(2px);
        }
        
        .document-item.selected {
            background: #e6f7ff;
            border-color: #91d5ff;
        }
        
        .document-item:last-child {
            border-bottom: none;
        }
        
        .item-checkbox {
            margin-right: 12px;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        .item-icon {
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #f5f5f5;
        }
        
        .file-icon {
            font-size: 20px;
        }
        
        .file-icon.pdf { color: #ff4d4f; }
        .file-icon.word { color: #1890ff; }
        .file-icon.txt { color: #52c41a; }
        .file-icon.md { color: #722ed1; }
        .file-icon.default { color: #8c8c8c; }
        
        .item-content {
            flex: 1;
            min-width: 0;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .item-title {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 400px;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-available { 
            background-color: #f6ffed; 
            color: #52c41a; 
            border: 1px solid #b7eb8f; 
        }
        
        .status-processing { 
            background-color: #fff7e6; 
            color: #fa8c16; 
            border: 1px solid #ffd591; 
        }
        
        .item-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .item-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 16px;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 32px;
            height: 18px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 18px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #1890ff;
        }
        
        input:checked + .slider:before {
            transform: translateX(14px);
        }
        
        .more-btn {
            padding: 4px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.2s ease;
        }
        
        .more-btn:hover {
            background: #f0f0f0;
        }
        
        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            padding: 40px 20px;
        }
        
        .empty-content {
            text-align: center;
            max-width: 400px;
        }
        
        .empty-icon {
            font-size: 64px;
            color: #d9d9d9;
            margin-bottom: 16px;
        }
        
        .empty-title {
            font-size: 18px;
            color: #595959;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .empty-description {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .highlight h3 {
            margin-bottom: 8px;
            font-size: 18px;
        }
        
        .highlight p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="highlight">
        <h3>🎨 优化后的文档管理UI</h3>
        <p>现代化卡片设计 • 批量操作 • 响应式布局 • 更好的用户体验</p>
    </div>
    
    <div class="container">
        <!-- 卡片头部 -->
        <div class="card-header">
            <div class="header-left">
                <span class="header-icon">📄</span>
                <div class="header-content">
                    <h3 class="header-title">文档管理</h3>
                    <div class="header-subtitle">3 个文档</div>
                </div>
            </div>
            <div class="header-actions">
                <input type="text" class="search-input" placeholder="搜索文档..." />
                <button class="btn btn-primary">
                    ➕ 添加文档
                </button>
            </div>
        </div>
        
        <!-- 批量操作栏 -->
        <div class="batch-actions">
            <div class="batch-left">
                <label>
                    <input type="checkbox" class="checkbox" /> 全选
                </label>
                <span class="selected-count">已选择 2 项</span>
            </div>
            <div class="batch-right">
                <button class="btn btn-small">🗑️ 批量删除</button>
                <button class="btn btn-small">✅ 批量启用</button>
                <button class="btn btn-small">⏸️ 批量禁用</button>
            </div>
        </div>
        
        <!-- 文档列表 -->
        <div class="document-list">
            <div class="document-item selected">
                <div class="item-checkbox">
                    <input type="checkbox" class="checkbox" checked />
                </div>
                
                <div class="item-icon">
                    <span class="file-icon pdf">📄</span>
                </div>
                
                <div class="item-content">
                    <div class="item-header">
                        <h4 class="item-title">2024年秋季招聘中学数学岗位计划.pdf</h4>
                        <div class="item-status">
                            <span class="status-tag status-available">可用</span>
                        </div>
                    </div>
                    
                    <div class="item-meta">
                        <span class="meta-item">
                            📊 0 分段
                        </span>
                        <span class="meta-item">
                            📝 2.6k 字符
                        </span>
                        <span class="meta-item">
                            🕒 2025-05-18 23:04
                        </span>
                    </div>
                </div>
                
                <div class="item-actions">
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                    <button class="more-btn">⋯</button>
                </div>
            </div>
            
            <div class="document-item selected">
                <div class="item-checkbox">
                    <input type="checkbox" class="checkbox" checked />
                </div>
                
                <div class="item-icon">
                    <span class="file-icon word">📝</span>
                </div>
                
                <div class="item-content">
                    <div class="item-header">
                        <h4 class="item-title">08社会服务.docx</h4>
                        <div class="item-status">
                            <span class="status-tag status-available">可用</span>
                        </div>
                    </div>
                    
                    <div class="item-meta">
                        <span class="meta-item">
                            📊 0 分段
                        </span>
                        <span class="meta-item">
                            📝 189 字符
                        </span>
                        <span class="meta-item">
                            🕒 2025-05-16 05:14
                        </span>
                    </div>
                </div>
                
                <div class="item-actions">
                    <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider"></span>
                    </label>
                    <button class="more-btn">⋯</button>
                </div>
            </div>
            
            <div class="document-item">
                <div class="item-checkbox">
                    <input type="checkbox" class="checkbox" />
                </div>
                
                <div class="item-icon">
                    <span class="file-icon word">📋</span>
                </div>
                
                <div class="item-content">
                    <div class="item-header">
                        <h4 class="item-title">07建设成效.docx</h4>
                        <div class="item-status">
                            <span class="status-tag status-processing">处理中</span>
                        </div>
                    </div>
                    
                    <div class="item-meta">
                        <span class="meta-item">
                            📊 2 分段
                        </span>
                        <span class="meta-item">
                            📝 842 字符
                        </span>
                        <span class="meta-item">
                            🕒 2025-05-16 05:14
                        </span>
                    </div>
                </div>
                
                <div class="item-actions">
                    <label class="switch">
                        <input type="checkbox">
                        <span class="slider"></span>
                    </label>
                    <button class="more-btn">⋯</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('优化后的文档管理UI加载完成');
            
            // 搜索功能
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    alert('🔍 搜索: ' + (this.value || '全部文档'));
                }
            });
            
            // 添加文档功能
            document.querySelector('.btn-primary').addEventListener('click', function() {
                alert('📁 打开文件选择对话框');
            });
            
            // 批量操作
            document.querySelectorAll('.batch-right .btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    alert(`执行批量操作: ${action}`);
                });
            });
            
            // 更多操作按钮
            document.querySelectorAll('.more-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const row = this.closest('.document-item');
                    const fileName = row.querySelector('.item-title').textContent;
                    alert(`操作菜单: ${fileName}\n- 编辑\n- 删除`);
                });
            });
            
            // 开关切换
            document.querySelectorAll('.switch input').forEach(input => {
                input.addEventListener('change', function() {
                    const row = this.closest('.document-item');
                    const fileName = row.querySelector('.item-title').textContent;
                    const enabled = this.checked;
                    alert(`${fileName} 已${enabled ? '启用' : '禁用'}`);
                });
            });
            
            // 选择功能
            document.querySelectorAll('.item-checkbox input').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const item = this.closest('.document-item');
                    if (this.checked) {
                        item.classList.add('selected');
                    } else {
                        item.classList.remove('selected');
                    }
                });
            });
        });
    </script>
</body>
</html>
