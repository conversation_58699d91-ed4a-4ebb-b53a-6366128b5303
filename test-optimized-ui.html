<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的文档列表UI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #1890ff;
        }
        .search-bar {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 20px;
        }
        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        .search-btn {
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .upload-btn {
            background: #52c41a !important;
        }
        
        /* 文档列表样式 */
        .document-list {
            margin-top: 20px;
        }
        .document-item {
            display: flex;
            align-items: flex-start;
            padding: 16px;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            margin-bottom: 12px;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .document-item:hover {
            background: #f5f5f5;
            border-color: #d9d9d9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        .document-icon {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #e6f7ff;
            border-radius: 6px;
            margin-right: 12px;
            font-size: 18px;
            color: #1890ff;
        }
        .document-content {
            flex: 1;
            min-width: 0;
        }
        .document-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .document-title {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: calc(100% - 80px);
        }
        .document-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            color: #8c8c8c;
            font-size: 12px;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .document-actions {
            flex-shrink: 0;
            display: flex;
            gap: 4px;
            margin-left: 12px;
        }
        .action-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        .action-btn.danger:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
        }
        .status-tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-available { background-color: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .status-processing { background-color: #fff7e6; color: #fa8c16; border: 1px solid #ffd591; }
        .status-failed { background-color: #fff2f0; color: #ff4d4f; border: 1px solid #ffccc7; }
        
        .pagination {
            margin-top: 24px;
            text-align: center;
            padding: 16px 0;
            border-top: 1px solid #f0f0f0;
        }
        
        .highlight {
            background-color: #e6f7ff;
            border: 2px solid #1890ff;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .document-item {
                flex-direction: column;
                align-items: stretch;
            }
            .document-icon {
                align-self: flex-start;
                margin-bottom: 8px;
            }
            .document-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }
            .document-title {
                max-width: 100%;
            }
            .document-meta {
                flex-direction: column;
                gap: 8px;
            }
            .document-actions {
                margin-left: 0;
                margin-top: 12px;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📋 优化后的文档列表UI</h1>
        <p style="color: #666;">✨ 使用自定义组件替代DataTable，提供更现代化的用户体验</p>
        
        <!-- 搜索和上传 -->
        <div class="section highlight">
            <h3>🔍 文档管理</h3>
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="文档名称" />
                <button class="search-btn">🔍 搜索</button>
                <button class="search-btn upload-btn">➕ 添加文档</button>
            </div>
        </div>

        <!-- 优化后的文档列表 -->
        <div class="section">
            <h3>📄 文档列表 <small style="color: #52c41a;">(新UI设计)</small></h3>
            <div class="document-list">
                <!-- 文档项 1 -->
                <div class="document-item">
                    <div class="document-icon">📄</div>
                    <div class="document-content">
                        <div class="document-header">
                            <h4 class="document-title">2024年秋季招聘中学数学岗位计划.pdf</h4>
                            <span class="status-tag status-available">可用</span>
                        </div>
                        <div class="document-meta">
                            <span class="meta-item">🏷️ 通用</span>
                            <span class="meta-item">📝 2.6k 字符</span>
                            <span class="meta-item">🧩 0 分段</span>
                            <span class="meta-item">🕒 2025-05-16 23:04</span>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="action-btn">✏️ 编辑</button>
                        <button class="action-btn danger">🗑️ 删除</button>
                    </div>
                </div>

                <!-- 文档项 2 -->
                <div class="document-item">
                    <div class="document-icon">📝</div>
                    <div class="document-content">
                        <div class="document-header">
                            <h4 class="document-title">08社会服务.docx</h4>
                            <span class="status-tag status-available">可用</span>
                        </div>
                        <div class="document-meta">
                            <span class="meta-item">🏷️ 通用</span>
                            <span class="meta-item">📝 1.8k 字符</span>
                            <span class="meta-item">🧩 0 分段</span>
                            <span class="meta-item">🕒 2025-05-16 05:34</span>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="action-btn">✏️ 编辑</button>
                        <button class="action-btn danger">🗑️ 删除</button>
                    </div>
                </div>

                <!-- 文档项 3 -->
                <div class="document-item">
                    <div class="document-icon">📋</div>
                    <div class="document-content">
                        <div class="document-header">
                            <h4 class="document-title">07建设成效.docx</h4>
                            <span class="status-tag status-processing">处理中</span>
                        </div>
                        <div class="document-meta">
                            <span class="meta-item">🏷️ 通用</span>
                            <span class="meta-item">📝 1.8k 字符</span>
                            <span class="meta-item">🧩 2 分段</span>
                            <span class="meta-item">🕒 2025-05-16 05:34</span>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="action-btn">✏️ 编辑</button>
                        <button class="action-btn danger">🗑️ 删除</button>
                    </div>
                </div>

                <!-- 文档项 4 -->
                <div class="document-item">
                    <div class="document-icon">📄</div>
                    <div class="document-content">
                        <div class="document-header">
                            <h4 class="document-title">技术文档说明.md</h4>
                            <span class="status-tag status-failed">失败</span>
                        </div>
                        <div class="document-meta">
                            <span class="meta-item">🏷️ 技术</span>
                            <span class="meta-item">📝 5.2k 字符</span>
                            <span class="meta-item">🧩 8 分段</span>
                            <span class="meta-item">🕒 2025-05-15 14:22</span>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="action-btn">✏️ 编辑</button>
                        <button class="action-btn danger">🗑️ 删除</button>
                    </div>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <span>共 4 项，当前 1-4 项</span> | 
                <a href="#" style="margin: 0 8px;">上一页</a>
                <strong>1</strong>
                <a href="#" style="margin: 0 8px;">下一页</a>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="section" style="background-color: #f6ffed; border-color: #b7eb8f;">
            <h3 style="color: #52c41a;">✨ UI优化特点</h3>
            <ul>
                <li><strong>卡片式设计：</strong>每个文档以卡片形式展示，视觉层次清晰</li>
                <li><strong>图标识别：</strong>根据文件类型显示不同图标，便于快速识别</li>
                <li><strong>状态标签：</strong>彩色标签显示文档处理状态</li>
                <li><strong>悬停效果：</strong>鼠标悬停时卡片有阴影和颜色变化</li>
                <li><strong>响应式布局：</strong>在移动设备上自动调整布局</li>
                <li><strong>信息丰富：</strong>显示文档的所有关键信息</li>
                <li><strong>操作便捷：</strong>编辑和删除按钮位置固定，操作方便</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('优化后的文档列表UI测试页面加载完成');
            
            // 搜索功能
            document.querySelector('.search-btn:first-of-type').addEventListener('click', function() {
                const searchValue = document.querySelector('.search-input').value;
                alert('🔍 搜索: ' + (searchValue || '全部文档'));
            });
            
            // 添加文档功能
            document.querySelector('.upload-btn').addEventListener('click', function() {
                alert('📁 打开文件选择对话框');
            });
            
            // 文档操作
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.includes('编辑') ? '编辑' : '删除';
                    const item = this.closest('.document-item');
                    const title = item.querySelector('.document-title').textContent;
                    alert(`${action}文档: ${title}`);
                });
            });
        });
    </script>
</body>
</html>
