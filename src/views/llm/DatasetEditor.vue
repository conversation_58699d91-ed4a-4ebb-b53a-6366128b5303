<script setup>
import { getCurrentInstance, inject, onMounted, ref, computed } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import DatasetApi from '@/api/llm/dataset.js'
import UtilApi from '@/api/util.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.ai.model.llm.Dataset', ['title', 'description'])

const fields = [{
  title: '知识库名称',
  field: 'title',
  type: 'text',
  config: {
    promise: _config.title
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

// 数据集信息
const datasetInfo = ref(null)
const isDatasetCreated = computed(() => datasetInfo.value && datasetInfo.value.id)

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  callback (record) {
    const _promise = DatasetApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        // 保存数据集信息
        datasetInfo.value = { ...record, id: result.data }
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        // 更新数据集信息
        datasetInfo.value = { ...record }
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = DatasetApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const form = ref()

// 文档列表相关
const documents = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 表格选择相关
const checkAll = ref(false)
const indeterminate = ref(false)

// 计算选中数量
const selectedCount = computed(() => {
  return documents.value.filter(doc => doc.checked).length
})

const onCheckAllChange = (e) => {
  documents.value.forEach(doc => {
    doc.checked = e.target.checked
  })
  indeterminate.value = false
}

const onDocumentCheck = () => {
  const checkedCount = documents.value.filter(doc => doc.checked).length
  checkAll.value = checkedCount === documents.value.length
  indeterminate.value = checkedCount > 0 && checkedCount < documents.value.length
}

// 批量操作
const batchDelete = () => {
  const selectedDocs = documents.value.filter(doc => doc.checked)
  if (selectedDocs.length === 0) return

  FeedbackUtil.modal(`确定要删除选中的 ${selectedDocs.length} 个文档吗？`, 'confirm', {
    onOk () {
      const promises = selectedDocs.map(doc =>
        DatasetApi.removeDocument(doc.id, { showLoading: false })
      )
      return Promise.all(promises).then(() => {
        FeedbackUtil.message('批量删除成功', 'success')
        loadDocuments()
      })
    }
  })
}

const batchToggle = (enabled) => {
  const selectedDocs = documents.value.filter(doc => doc.checked)
  if (selectedDocs.length === 0) return

  selectedDocs.forEach(doc => {
    doc.enabled = enabled
  })
  FeedbackUtil.message(`已${enabled ? '启用' : '禁用'} ${selectedDocs.length} 个文档`, 'success')
}

// 搜索功能
const handleSearch = () => {
  pagination.value.current = 1
  loadDocuments(1)
}

// 添加文档功能
const handleAddDocument = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true
  input.accept = '.pdf,.doc,.docx,.txt,.md'
  input.onchange = (e) => {
    const files = Array.from(e.target.files)
    if (files.length > 0) {
      uploadDocuments(files)
    }
  }
  input.click()
}

// 文档操作
const editDocument = (document) => {
  // TODO: 实现编辑文档功能
  FeedbackUtil.message('编辑功能待实现', 'info')
}

const deleteDocument = (document) => {
  FeedbackUtil.modal('确定要删除这个文档吗？', 'confirm', {
    onOk () {
      return DatasetApi.removeDocument(document.id, {
        showLoading: false
      }).then(() => {
        FeedbackUtil.message('删除成功', 'success')
        loadDocuments()
      })
    }
  })
}

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    available: { color: 'success', text: '可用' },
    processing: { color: 'processing', text: '处理中' },
    failed: { color: 'error', text: '失败' }
  }
  return statusMap[status] || { color: 'default', text: '未知' }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0'
  if (size < 1000) return size.toString()
  if (size < 1000000) return (size / 1000).toFixed(1) + 'k'
  return (size / 1000000).toFixed(1) + 'M'
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载文档列表
const loadDocuments = async (page = 1) => {
  if (!isDatasetCreated.value) {
    documents.value = []
    pagination.value.total = 0
    return
  }

  loading.value = true

  try {
    const result = await DatasetApi.searchDocuments(
      pagination.value.pageSize,
      page - 1,
      datasetInfo.value.id,
      searchKeyword.value,
      {
        showLoading: false,
        toast: { success: false }
      }
    )

    // 处理数据
    const records = result.data.records || []
    records.forEach((record, idx) => {
      record.index = (page - 1) * pagination.value.pageSize + idx + 1
      record.status = record.status || 'available'
      record.category = record.category || '通用'
      record.characterCount = record.characterCount || 0
      record.segmentCount = record.segmentCount || 0
      record.checked = false
      record.enabled = record.enabled !== false
      record.availableSegments = record.availableSegments || record.segmentCount || 0
    })

    documents.value = records
    pagination.value.current = page
    pagination.value.total = result.data.total || 0
  } catch (error) {
    documents.value = []
    pagination.value.total = 0
    console.error('加载文档列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 分页变化处理
const handlePageChange = (page, pageSize) => {
  pagination.value.pageSize = pageSize
  loadDocuments(page)
}

// 表格选择功能
const onCheckAllChange = (e) => {
  documents.value.forEach(doc => {
    doc.checked = e.target.checked
  })
  indeterminate.value = false
}

const onDocumentCheck = () => {
  const checkedCount = documents.value.filter(doc => doc.checked).length
  checkAll.value = checkedCount === documents.value.length
  indeterminate.value = checkedCount > 0 && checkedCount < documents.value.length
}

// 切换文档启用状态
const toggleDocument = (document, enabled) => {
  document.enabled = enabled
  // TODO: 调用API更新文档状态
  FeedbackUtil.message(`文档已${enabled ? '启用' : '禁用'}`, 'success')
}

// 文档上传功能
const uploadDocuments = async (files) => {
  if (!isDatasetCreated.value) {
    FeedbackUtil.message('请先保存知识库信息', 'warning')
    return
  }

  if (files.length === 0) {
    return
  }

  try {
    FeedbackUtil.message('正在上传文档...', 'loading')

    const result = await DatasetApi.uploadDocument(null, datasetInfo.value.id, '', files, {
      showLoading: true
    })

    // 检查上传结果
    const successCount = result.data.filter(item => item.success).length
    const failCount = result.data.length - successCount

    if (successCount > 0) {
      FeedbackUtil.message(`成功上传 ${successCount} 个文档${failCount > 0 ? `，${failCount} 个失败` : ''}`, 'success')
      // 刷新文档列表
      loadDocuments()
    } else {
      FeedbackUtil.message('文档上传失败', 'error')
    }
  } catch (error) {
    FeedbackUtil.message(error.message || '文档上传失败', 'error')
  }
}

onMounted(() => {
  if (proxy.$route.query.id) {
    DatasetApi.get(proxy.$route.query.id, {
      loading: true,
      toast: {
        success: false
      }
    })
      .then(result => {
        // 更新表单和数据集信息
        datasetInfo.value = result.data
        form.value.setModel(result.data)
        // 加载文档列表
        loadDocuments()
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    form.value.setModel({
      // 设置默认值
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})

</script>

<template>
  <!-- 知识库基本信息 -->
  <div class="layout-content-panel">
    <GenericFrom
      ref="form"
      :fields="fields"
      :actions="actions"
    />
  </div>

  <!-- 文档列表 - 只在知识库已创建时显示 -->
  <template v-if="isDatasetCreated">
    <div class="layout-content-panel">
      <a-card class="document-card" :bordered="false">
        <!-- 卡片头部 -->
        <template #title>
          <div class="card-header">
            <div class="header-left">
              <file-text-outlined class="header-icon" />
              <div class="header-content">
                <h3 class="header-title">文档管理</h3>
                <span class="header-subtitle">{{ documents.length }} 个文档</span>
              </div>
            </div>
            <div class="header-actions">
              <a-space>
                <a-input
                  v-model:value="searchKeyword"
                  placeholder="搜索文档..."
                  size="middle"
                  style="width: 240px"
                  @press-enter="handleSearch"
                  allow-clear
                >
                  <template #prefix>
                    <search-outlined />
                  </template>
                </a-input>
                <a-button type="primary" @click="handleAddDocument" size="middle">
                  <plus-outlined />
                  添加文档
                </a-button>
              </a-space>
            </div>
          </div>
        </template>

        <!-- 文档列表 -->
        <a-spin :spinning="loading" size="large">
          <div class="document-list-container">
            <!-- 批量操作栏 -->
            <div v-if="documents.length > 0" class="batch-actions">
              <div class="batch-left">
                <a-checkbox
                  :indeterminate="indeterminate"
                  v-model:checked="checkAll"
                  @change="onCheckAllChange"
                >
                  全选
                </a-checkbox>
                <span class="selected-count" v-if="selectedCount > 0">
                  已选择 {{ selectedCount }} 项
                </span>
              </div>
              <div class="batch-right" v-if="selectedCount > 0">
                <a-button size="small" @click="batchDelete">
                  <delete-outlined />
                  批量删除
                </a-button>
                <a-button size="small" @click="batchToggle(true)">
                  <check-outlined />
                  批量启用
                </a-button>
                <a-button size="small" @click="batchToggle(false)">
                  <stop-outlined />
                  批量禁用
                </a-button>
              </div>
            </div>

            <!-- 文档列表 -->
            <div v-if="documents.length > 0" class="document-list">
              <div
                v-for="(doc, index) in documents"
                :key="doc.id"
                class="document-item"
                :class="{ 'selected': doc.checked }"
              >
                <div class="item-checkbox">
                  <a-checkbox v-model:checked="doc.checked" @change="onDocumentCheck" />
                </div>

                <div class="item-icon">
                  <file-text-outlined v-if="doc.name?.endsWith('.txt')" class="file-icon txt" />
                  <file-pdf-outlined v-else-if="doc.name?.endsWith('.pdf')" class="file-icon pdf" />
                  <file-word-outlined v-else-if="doc.name?.match(/\.(doc|docx)$/)" class="file-icon word" />
                  <file-markdown-outlined v-else-if="doc.name?.endsWith('.md')" class="file-icon md" />
                  <file-outlined v-else class="file-icon default" />
                </div>

                <div class="item-content">
                  <div class="item-header">
                    <h4 class="item-title" :title="doc.name">{{ doc.name }}</h4>
                    <div class="item-status">
                      <a-tag :color="getStatusTag(doc.status).color" size="small">
                        {{ getStatusTag(doc.status).text }}
                      </a-tag>
                    </div>
                  </div>

                  <div class="item-meta">
                    <span class="meta-item">
                      <block-outlined />
                      {{ doc.segmentCount || 0 }} 分段
                    </span>
                    <span class="meta-item">
                      <file-text-outlined />
                      {{ formatFileSize(doc.characterCount) }} 字符
                    </span>
                    <span class="meta-item">
                      <clock-circle-outlined />
                      {{ formatTime(doc.createTime) }}
                    </span>
                  </div>
                </div>

                <div class="item-actions">
                  <a-tooltip title="启用/禁用">
                    <a-switch
                      size="small"
                      :checked="doc.enabled !== false"
                      @change="(checked) => toggleDocument(doc, checked)"
                    />
                  </a-tooltip>
                  <a-dropdown :trigger="['click']" placement="bottomRight">
                    <a-button type="text" size="small" class="action-more">
                      <more-outlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="editDocument(doc)">
                          <edit-outlined />
                          编辑
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item @click="deleteDocument(doc)" class="danger-item">
                          <delete-outlined />
                          删除
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-state">
              <div class="empty-content">
                <file-add-outlined class="empty-icon" />
                <h3 class="empty-title">暂无文档</h3>
                <p class="empty-description">上传您的第一个文档开始构建知识库</p>
                <a-button type="primary" @click="handleAddDocument" size="large">
                  <plus-outlined />
                  添加文档
                </a-button>
              </div>
            </div>
          </div>
        </a-spin>

        <!-- 分页 -->
        <div v-if="pagination.total > 0" class="pagination-wrapper">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `共 ${total} 项`"
            @change="handlePageChange"
            size="small"
          />
        </div>
      </a-card>
    </div>
  </template>
</template>

<style lang="less" scoped>
// 文档卡片样式
.document-card {
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    padding: 0;
  }

  .ant-card-head-title {
    padding: 20px 24px;
  }

  .ant-card-body {
    padding: 0;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .header-icon {
      font-size: 20px;
      color: #1890ff;
    }

    .header-content {
      .header-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .header-subtitle {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 2px;
      }
    }
  }

  .header-actions {
    .ant-space {
      align-items: center;
    }
  }
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .document-title {
    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .document-subtitle {
      font-size: 12px;
      color: #8c8c8c;
      line-height: 1.4;
    }
  }

  .document-actions {
    display: flex;
    gap: 8px;
  }
}

.document-table-container {
  min-height: 200px;
}

.document-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;

  th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    text-align: left;
    font-weight: 500;
    color: #8c8c8c;
    font-size: 12px;
    white-space: nowrap;

    &:first-child {
      padding-left: 20px;
    }

    &:last-child {
      padding-right: 20px;
    }
  }

  .document-row {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    td {
      padding: 12px 16px;
      vertical-align: middle;

      &:first-child {
        padding-left: 20px;
      }

      &:last-child {
        padding-right: 20px;
      }
    }
  }

  .row-index {
    color: #8c8c8c;
    font-size: 12px;
  }

  .document-name {
    .name-cell {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .file-icon {
      color: #1890ff;
      font-size: 14px;
      flex-shrink: 0;
    }

    .file-name {
      color: #262626;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 300px;
    }
  }

  .text-center {
    text-align: center;
    color: #595959;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #8c8c8c;

  .empty-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: #595959;
  }

  .empty-description {
    font-size: 14px;
    margin: 0;
    color: #8c8c8c;
  }
}

.pagination-wrapper {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  justify-content: center;
}

// 下拉菜单危险项样式
:deep(.danger-item) {
  color: #ff4d4f !important;

  &:hover {
    background: #fff2f0 !important;
  }

  .anticon {
    color: #ff4d4f !important;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .document-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .document-table {
    font-size: 12px;

    th, td {
      padding: 8px 12px;

      &:first-child {
        padding-left: 16px;
      }

      &:last-child {
        padding-right: 16px;
      }
    }

    .document-name .file-name {
      max-width: 200px;
    }
  }

  .pagination-wrapper {
    padding: 12px 16px;
  }

  // 新UI响应式
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-actions {
      width: 100%;

      .ant-space {
        width: 100%;
        justify-content: space-between;
      }
    }
  }

  .document-item {
    padding: 12px 16px;

    .item-icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;

      .file-icon {
        font-size: 16px;
      }
    }

    .item-content {
      .item-header .item-title {
        max-width: 200px;
      }

      .item-meta {
        gap: 12px;
        flex-wrap: wrap;
      }
    }
  }

  .batch-actions {
    padding: 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .batch-right {
      width: 100%;
      justify-content: flex-start;
    }
  }
}

// 批量操作栏
.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;

  .batch-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .selected-count {
      font-size: 12px;
      color: #1890ff;
      font-weight: 500;
    }
  }

  .batch-right {
    display: flex;
    gap: 8px;
  }
}

// 文档列表容器
.document-list-container {
  min-height: 200px;
}

.document-list {
  padding: 0;
}

// 文档项样式
.document-item {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background: #f8f9fa;
  }

  &.selected {
    background: #e6f7ff;
    border-color: #91d5ff;
  }

  &:last-child {
    border-bottom: none;
  }

  .item-checkbox {
    margin-right: 12px;
  }

  .item-icon {
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #f5f5f5;

    .file-icon {
      font-size: 20px;

      &.pdf { color: #ff4d4f; }
      &.word { color: #1890ff; }
      &.txt { color: #52c41a; }
      &.md { color: #722ed1; }
      &.default { color: #8c8c8c; }
    }
  }

  .item-content {
    flex: 1;
    min-width: 0;

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .item-title {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 400px;
      }
    }

    .item-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #8c8c8c;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;

        .anticon {
          font-size: 12px;
        }
      }
    }
  }

  .item-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;

    .action-more {
      &:hover {
        background: #f0f0f0;
      }
    }
  }
}
</style>
