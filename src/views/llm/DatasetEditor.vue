<script setup>
import { getCurrentInstance, inject, onMounted, ref, computed } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import DatasetApi from '@/api/llm/dataset.js'
import UtilApi from '@/api/util.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.ai.model.llm.Dataset', ['title', 'description'])

const fields = [{
  title: '知识库名称',
  field: 'title',
  type: 'text',
  config: {
    promise: _config.title
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

// 数据集信息
const datasetInfo = ref(null)
const isDatasetCreated = computed(() => datasetInfo.value && datasetInfo.value.id)

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  callback (record) {
    const _promise = DatasetApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        // 保存数据集信息
        datasetInfo.value = { ...record, id: result.data }
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        // 更新数据集信息
        datasetInfo.value = { ...record }
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = DatasetApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const form = ref()

// 文档列表相关
const documents = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 表格选择相关
const checkAll = ref(false)
const indeterminate = ref(false)

// 搜索功能
const handleSearch = () => {
  pagination.value.current = 1
  loadDocuments(1)
}

// 添加文档功能
const handleAddDocument = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true
  input.accept = '.pdf,.doc,.docx,.txt,.md'
  input.onchange = (e) => {
    const files = Array.from(e.target.files)
    if (files.length > 0) {
      uploadDocuments(files)
    }
  }
  input.click()
}

// 文档操作
const editDocument = (document) => {
  // TODO: 实现编辑文档功能
  FeedbackUtil.message('编辑功能待实现', 'info')
}

const deleteDocument = (document) => {
  FeedbackUtil.modal('确定要删除这个文档吗？', 'confirm', {
    onOk () {
      return DatasetApi.removeDocument(document.id, {
        showLoading: false
      }).then(() => {
        FeedbackUtil.message('删除成功', 'success')
        loadDocuments()
      })
    }
  })
}

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    available: { color: 'success', text: '可用' },
    processing: { color: 'processing', text: '处理中' },
    failed: { color: 'error', text: '失败' }
  }
  return statusMap[status] || { color: 'default', text: '未知' }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0'
  if (size < 1000) return size.toString()
  if (size < 1000000) return (size / 1000).toFixed(1) + 'k'
  return (size / 1000000).toFixed(1) + 'M'
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载文档列表
const loadDocuments = async (page = 1) => {
  if (!isDatasetCreated.value) {
    documents.value = []
    pagination.value.total = 0
    return
  }

  loading.value = true
  const filters = searchbar.value ? searchbar.value.model() : {}

  try {
    const result = await DatasetApi.searchDocuments(
      pagination.value.pageSize,
      page - 1,
      datasetInfo.value.id,
      filters.name,
      {
        showLoading: false,
        toast: { success: false }
      }
    )

    // 处理数据
    const records = result.data.records || []
    records.forEach((record, idx) => {
      record.index = (page - 1) * pagination.value.pageSize + idx + 1
      record.status = record.status || 'available'
      record.category = record.category || '通用'
      record.characterCount = record.characterCount || 0
      record.segmentCount = record.segmentCount || 0
      record.checked = false
      record.enabled = record.enabled !== false
      record.availableSegments = record.availableSegments || record.segmentCount || 0
    })

    documents.value = records
    pagination.value.current = page
    pagination.value.total = result.data.total || 0
  } catch (error) {
    documents.value = []
    pagination.value.total = 0
    console.error('加载文档列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 分页变化处理
const handlePageChange = (page, pageSize) => {
  pagination.value.pageSize = pageSize
  loadDocuments(page)
}

// 表格选择功能
const onCheckAllChange = (e) => {
  documents.value.forEach(doc => {
    doc.checked = e.target.checked
  })
  indeterminate.value = false
}

const onDocumentCheck = () => {
  const checkedCount = documents.value.filter(doc => doc.checked).length
  checkAll.value = checkedCount === documents.value.length
  indeterminate.value = checkedCount > 0 && checkedCount < documents.value.length
}

// 切换文档启用状态
const toggleDocument = (document, enabled) => {
  document.enabled = enabled
  // TODO: 调用API更新文档状态
  FeedbackUtil.message(`文档已${enabled ? '启用' : '禁用'}`, 'success')
}

// 文档上传功能
const uploadDocuments = async (files) => {
  if (!isDatasetCreated.value) {
    FeedbackUtil.message('请先保存知识库信息', 'warning')
    return
  }

  if (files.length === 0) {
    return
  }

  try {
    FeedbackUtil.message('正在上传文档...', 'loading')

    const result = await DatasetApi.uploadDocument(null, datasetInfo.value.id, '', files, {
      showLoading: true
    })

    // 检查上传结果
    const successCount = result.data.filter(item => item.success).length
    const failCount = result.data.length - successCount

    if (successCount > 0) {
      FeedbackUtil.message(`成功上传 ${successCount} 个文档${failCount > 0 ? `，${failCount} 个失败` : ''}`, 'success')
      // 刷新文档列表
      loadDocuments()
    } else {
      FeedbackUtil.message('文档上传失败', 'error')
    }
  } catch (error) {
    FeedbackUtil.message(error.message || '文档上传失败', 'error')
  }
}

onMounted(() => {
  if (proxy.$route.query.id) {
    DatasetApi.get(proxy.$route.query.id, {
      loading: true,
      toast: {
        success: false
      }
    })
      .then(result => {
        // 更新表单和数据集信息
        datasetInfo.value = result.data
        form.value.setModel(result.data)
        // 加载文档列表
        loadDocuments()
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    form.value.setModel({
      // 设置默认值
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})

</script>

<template>
  <!-- 知识库基本信息 -->
  <div class="layout-content-panel">
    <GenericFrom
      ref="form"
      :fields="fields"
      :actions="actions"
    />
  </div>

  <!-- 文档列表 - 只在知识库已创建时显示 -->
  <template v-if="isDatasetCreated">
    <!-- 搜索栏 -->
    <div class="layout-content-panel">
      <SearchBar
        ref="searchbar"
        :fields="searchbarOptions.fields"
        :actions="searchbarOptions.actions"
      />
    </div>

    <!-- 文档列表 -->
    <div class="layout-content-panel">
      <div class="document-container">
        <!-- 头部工具栏 -->
        <div class="document-header">
          <div class="document-title">
            <h3>文档</h3>
            <span class="document-subtitle">知识库中的所有文档，每个文档都可以进行编辑和删除操作</span>
          </div>
          <div class="document-actions">
            <a-button type="text" size="small">
              <unordered-list-outlined />
              不显示
            </a-button>
          </div>
        </div>

        <!-- 文档表格 -->
        <a-spin :spinning="loading">
          <div class="document-table-container">
            <table class="document-table" v-if="documents.length > 0">
              <thead>
                <tr>
                  <th width="40">
                    <a-checkbox :indeterminate="indeterminate" v-model:checked="checkAll" @change="onCheckAllChange" />
                  </th>
                  <th width="40">#</th>
                  <th>名称</th>
                  <th width="80">分段数量</th>
                  <th width="80">字符数</th>
                  <th width="80">可用分段</th>
                  <th width="140">上传时间 <caret-down-outlined /></th>
                  <th width="80">状态</th>
                  <th width="80">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(doc, index) in documents" :key="doc.id" class="document-row">
                  <td>
                    <a-checkbox v-model:checked="doc.checked" @change="onDocumentCheck" />
                  </td>
                  <td class="row-index">{{ doc.index }}</td>
                  <td class="document-name">
                    <div class="name-cell">
                      <file-text-outlined v-if="doc.name?.endsWith('.txt')" class="file-icon" />
                      <file-pdf-outlined v-else-if="doc.name?.endsWith('.pdf')" class="file-icon" />
                      <file-word-outlined v-else-if="doc.name?.match(/\.(doc|docx)$/)" class="file-icon" />
                      <file-markdown-outlined v-else-if="doc.name?.endsWith('.md')" class="file-icon" />
                      <file-outlined v-else class="file-icon" />
                      <span class="file-name" :title="doc.name">{{ doc.name }}</span>
                    </div>
                  </td>
                  <td class="text-center">{{ doc.segmentCount || 0 }}</td>
                  <td class="text-center">{{ formatFileSize(doc.characterCount) }}</td>
                  <td class="text-center">{{ doc.availableSegments || doc.segmentCount || 0 }}</td>
                  <td class="text-center">{{ formatTime(doc.createTime) }}</td>
                  <td class="text-center">
                    <a-tag :color="getStatusTag(doc.status).color" size="small">
                      {{ getStatusTag(doc.status).text }}
                    </a-tag>
                  </td>
                  <td class="text-center">
                    <div class="action-buttons">
                      <a-switch size="small" :checked="doc.enabled !== false" @change="(checked) => toggleDocument(doc, checked)" />
                      <a-dropdown :trigger="['click']">
                        <a-button type="text" size="small">
                          <more-outlined />
                        </a-button>
                        <template #overlay>
                          <a-menu>
                            <a-menu-item @click="editDocument(doc)">
                              <edit-outlined />
                              编辑
                            </a-menu-item>
                            <a-menu-item @click="deleteDocument(doc)" class="danger-item">
                              <delete-outlined />
                              删除
                            </a-menu-item>
                          </a-menu>
                        </template>
                      </a-dropdown>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- 空状态 -->
            <div v-else class="empty-state">
              <file-text-outlined class="empty-icon" />
              <p class="empty-text">暂无文档</p>
              <p class="empty-description">点击右上角"添加文档"按钮开始上传文档</p>
            </div>
          </div>
        </a-spin>

        <!-- 分页 -->
        <div v-if="pagination.total > 0" class="pagination-wrapper">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `共 ${total} 项`"
            @change="handlePageChange"
            size="small"
          />
        </div>
      </div>
    </div>
  </template>
</template>

<style lang="less" scoped>
.document-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .document-title {
    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .document-subtitle {
      font-size: 12px;
      color: #8c8c8c;
      line-height: 1.4;
    }
  }

  .document-actions {
    display: flex;
    gap: 8px;
  }
}

.document-table-container {
  min-height: 200px;
}

.document-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;

  th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    text-align: left;
    font-weight: 500;
    color: #8c8c8c;
    font-size: 12px;
    white-space: nowrap;

    &:first-child {
      padding-left: 20px;
    }

    &:last-child {
      padding-right: 20px;
    }
  }

  .document-row {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f8f9fa;
    }

    &:last-child {
      border-bottom: none;
    }

    td {
      padding: 12px 16px;
      vertical-align: middle;

      &:first-child {
        padding-left: 20px;
      }

      &:last-child {
        padding-right: 20px;
      }
    }
  }

  .row-index {
    color: #8c8c8c;
    font-size: 12px;
  }

  .document-name {
    .name-cell {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .file-icon {
      color: #1890ff;
      font-size: 14px;
      flex-shrink: 0;
    }

    .file-name {
      color: #262626;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 300px;
    }
  }

  .text-center {
    text-align: center;
    color: #595959;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #8c8c8c;

  .empty-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: #595959;
  }

  .empty-description {
    font-size: 14px;
    margin: 0;
    color: #8c8c8c;
  }
}

.pagination-wrapper {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  justify-content: center;
}

// 下拉菜单危险项样式
:deep(.danger-item) {
  color: #ff4d4f !important;

  &:hover {
    background: #fff2f0 !important;
  }

  .anticon {
    color: #ff4d4f !important;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .document-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .document-table {
    font-size: 12px;

    th, td {
      padding: 8px 12px;

      &:first-child {
        padding-left: 16px;
      }

      &:last-child {
        padding-right: 16px;
      }
    }

    .document-name .file-name {
      max-width: 200px;
    }
  }

  .pagination-wrapper {
    padding: 12px 16px;
  }
}
</style>
