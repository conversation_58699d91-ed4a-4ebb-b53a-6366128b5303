<script setup>
import { getCurrentInstance, inject, onMounted, ref, computed } from 'vue'
import GenericFrom from '@/components/GenericForm.vue'
import SearchBar from '@/components/SearchBar.vue'
import DatasetApi from '@/api/llm/dataset.js'
import UtilApi from '@/api/util.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.sparrow.ai.model.llm.Dataset', ['title', 'description'])

const fields = [{
  title: '知识库名称',
  field: 'title',
  type: 'text',
  config: {
    promise: _config.title
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '创建人员',
  field: 'creatorName',
  type: 'label'
}, {
  title: '创建时间',
  field: 'createTime',
  type: 'label'
}]

// 数据集信息
const datasetInfo = ref(null)
const isDatasetCreated = computed(() => datasetInfo.value && datasetInfo.value.id)

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  callback (record) {
    const _promise = DatasetApi.save(record)

    _promise.then(result => {
      if (record.id == null) {
        // 保存数据集信息
        datasetInfo.value = { ...record, id: result.data }
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        // 更新数据集信息
        datasetInfo.value = { ...record }
        reloadPage()
      }
    })

    return _promise
  }
}, {
  callback (record) {
    const _promise = DatasetApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const form = ref()

// 文档列表相关
const searchbar = ref()
const documentTable = ref()

// 搜索栏配置
const searchbarOptions = {
  fields: [{
    title: '文档名称',
    field: 'name',
    type: 'text',
    icon: 'SearchOutlined'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      documentTable.value.load()
    }
  }, {
    title: '添加文档',
    icon: 'PlusOutlined',
    callback () {
      // 触发文件选择
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = '.pdf,.doc,.docx,.txt,.md'
      input.onchange = (e) => {
        const files = Array.from(e.target.files)
        if (files.length > 0) {
          uploadDocuments(files)
        }
      }
      input.click()
    }
  }]
}

// 文档表格列配置
const documentColumns = [{
  title: '#',
  dataIndex: 'index',
  width: 60
}, {
  title: '名称',
  dataIndex: 'name',
  ellipsis: true
}, {
  title: '分类类型',
  dataIndex: 'category',
  width: 100
}, {
  title: '字符数',
  dataIndex: 'characterCount',
  width: 100
}, {
  title: '分段数',
  dataIndex: 'segmentCount',
  width: 100
}, {
  title: '上传时间',
  dataIndex: 'createTime',
  width: 180,
  type: 'datetime'
}, {
  title: '状态',
  dataIndex: 'status',
  width: 100,
  type: 'select',
  config: {
    options: [{
      label: '可用',
      value: 'available'
    }, {
      label: '处理中',
      value: 'processing'
    }, {
      label: '失败',
      value: 'failed'
    }]
  }
}]

// 文档表格操作
const documentActions = [{
  title: '编辑',
  icon: 'EditOutlined',
  callback (record) {
    // TODO: 实现编辑文档功能
    FeedbackUtil.message('编辑功能待实现', 'info')
  }
}, {
  title: '删除',
  icon: 'DeleteOutlined',
  danger: true,
  callback (record) {
    FeedbackUtil.modal('确定要删除这个文档吗？', 'confirm', {
      onOk () {
        return DatasetApi.removeDocument(record.id, {
          showLoading: false
        }).then(() => {
          FeedbackUtil.message('删除成功', 'success')
          documentTable.value.load()
        })
      }
    })
  }
}]

// 加载文档列表
const loadDocuments = (count, index, sorters) => {
  if (!isDatasetCreated.value) {
    return Promise.resolve({
      total: 0,
      records: []
    })
  }

  const filters = searchbar.value ? searchbar.value.model() : {}

  return new Promise(resolve => {
    DatasetApi.searchDocuments(count, index, datasetInfo.value.id, filters.name, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      // 处理数据，添加状态等信息
      const records = result.data.records || []
      records.forEach((record, idx) => {
        record.index = index * count + idx + 1
        record.status = record.status || 'available'
        record.category = record.category || '通用'
        record.characterCount = record.characterCount || 0
        record.segmentCount = record.segmentCount || 0
      })
      resolve({
        total: result.data.total || 0,
        records
      })
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

// 文档上传功能
const uploadDocuments = async (files) => {
  if (!isDatasetCreated.value) {
    FeedbackUtil.message('请先保存知识库信息', 'warning')
    return
  }

  if (files.length === 0) {
    return
  }

  try {
    FeedbackUtil.message('正在上传文档...', 'loading')

    const result = await DatasetApi.uploadDocument(null, datasetInfo.value.id, '', files, {
      showLoading: true
    })

    // 检查上传结果
    const successCount = result.data.filter(item => item.success).length
    const failCount = result.data.length - successCount

    if (successCount > 0) {
      FeedbackUtil.message(`成功上传 ${successCount} 个文档${failCount > 0 ? `，${failCount} 个失败` : ''}`, 'success')
      // 刷新文档列表
      if (documentTable.value) {
        documentTable.value.load()
      }
    } else {
      FeedbackUtil.message('文档上传失败', 'error')
    }
  } catch (error) {
    FeedbackUtil.message(error.message || '文档上传失败', 'error')
  }
}

onMounted(() => {
  if (proxy.$route.query.id) {
    DatasetApi.get(proxy.$route.query.id, {
      loading: true,
      toast: {
        success: false
      }
    })
      .then(result => {
        // 更新表单和数据集信息
        datasetInfo.value = result.data
        form.value.setModel(result.data)
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    form.value.setModel({
      // 设置默认值
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})

</script>

<template>
  <!-- 知识库基本信息 -->
  <div class="layout-content-panel">
    <GenericFrom
      ref="form"
      :fields="fields"
      :actions="actions"
    />
  </div>

  <!-- 文档列表 - 只在知识库已创建时显示 -->
  <template v-if="isDatasetCreated">
    <!-- 搜索栏 -->
    <div class="layout-content-panel">
      <SearchBar
        ref="searchbar"
        :fields="searchbarOptions.fields"
        :actions="searchbarOptions.actions"
      />
    </div>

    <!-- 文档列表 -->
    <div class="layout-content-panel">
      <DataTable
        ref="documentTable"
        :columns="documentColumns"
        :actions="documentActions"
        :load="loadDocuments"
        :load-after-initialized="true"
        :pagination="{
          current: 1,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true
        }"
      />
    </div>
  </template>
</template>
